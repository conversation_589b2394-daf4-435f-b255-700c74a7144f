Metadata-Version: 2.1
Name: langchain-deepseek
Version: 0.1.3
Summary: An integration package connecting DeepSeek and LangChain
License: MIT
Project-URL: Source Code, https://github.com/langchain-ai/langchain/tree/master/libs/partners/deepseek
Project-URL: Release Notes, https://github.com/langchain-ai/langchain/releases?q=tag%3A%22deepseek%3D%3D0%22&expanded=true
Project-URL: repository, https://github.com/langchain-ai/langchain
Requires-Python: <4.0,>=3.9
Requires-Dist: langchain-core<1.0.0,>=0.3.47
Requires-Dist: langchain-openai<1.0.0,>=0.3.9
Description-Content-Type: text/markdown

# langchain-deepseek

This package contains the LangChain integration with the DeepSeek API

## Installation

```bash
pip install -U langchain-deepseek
```

And you should configure credentials by setting the following environment variables:

* `DEEPSEEK_API_KEY`

## Chat Models

`ChatDeepSeek` class exposes chat models from DeepSeek.

```python
from langchain_deepseek import ChatDeepSeek

llm = ChatDeepSeek(model="deepseek-chat")
llm.invoke("Sing a ballad of LangChain.")
```
