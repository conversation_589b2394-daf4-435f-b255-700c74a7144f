../../../bin/browser-use,sha256=Hz9Z_xKkiirjYsHEONodZoekliXx6B-NJ1ckM5Ionoc,247
../../../bin/browseruse,sha256=Hz9Z_xKkiirjYsHEONodZoekliXx6B-NJ1ckM5Ionoc,247
browser_use-0.1.48.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
browser_use-0.1.48.dist-info/METADATA,sha256=k-oFuRoZ-m_3f1iWca-CAIPeGm906Dqf4UaqcnMu0WE,9272
browser_use-0.1.48.dist-info/RECORD,,
browser_use-0.1.48.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
browser_use-0.1.48.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
browser_use-0.1.48.dist-info/entry_points.txt,sha256=NceUXLtKZs9AznxXL8P1rxVQVpF8jyk0x3SXZGhU1VE,87
browser_use-0.1.48.dist-info/licenses/LICENSE,sha256=E1xXZxsO6VdmmwWgygDMBvZFSW01Hi5zKDLG4nbaml4,1069
browser_use/__init__.py,sha256=Vb7Gsjy1Z5BolJ9ATyddYv74vcIQ0Yb3XTiOf-UvRWw,1345
browser_use/__pycache__/__init__.cpython-312.pyc,,
browser_use/__pycache__/cli.cpython-312.pyc,,
browser_use/__pycache__/exceptions.cpython-312.pyc,,
browser_use/__pycache__/logging_config.cpython-312.pyc,,
browser_use/__pycache__/utils.cpython-312.pyc,,
browser_use/agent/__pycache__/gif.cpython-312.pyc,,
browser_use/agent/__pycache__/playwright_script_generator.cpython-312.pyc,,
browser_use/agent/__pycache__/playwright_script_helpers.cpython-312.pyc,,
browser_use/agent/__pycache__/prompts.cpython-312.pyc,,
browser_use/agent/__pycache__/service.cpython-312.pyc,,
browser_use/agent/__pycache__/views.cpython-312.pyc,,
browser_use/agent/gif.py,sha256=eU217IHSd3rbjzVJdb7YNg5mucXls6-ZIIq5mmWrxoo,10334
browser_use/agent/memory/__init__.py,sha256=X70G1C3hyfUIpxGCHlCzvb17whQFdf0hr72PHqZRfoI,146
browser_use/agent/memory/__pycache__/__init__.cpython-312.pyc,,
browser_use/agent/memory/__pycache__/service.cpython-312.pyc,,
browser_use/agent/memory/__pycache__/views.cpython-312.pyc,,
browser_use/agent/memory/service.py,sha256=Iv5dGeiFy37UPDSCywaW9oTdX67VhXVLrQU4TKWfbrQ,5667
browser_use/agent/memory/views.py,sha256=rOGfCWvUwvFE0UWi4ygbiTZE65Q4QwgJD9LB0-yw6H4,2281
browser_use/agent/message_manager/__pycache__/service.cpython-312.pyc,,
browser_use/agent/message_manager/__pycache__/utils.cpython-312.pyc,,
browser_use/agent/message_manager/__pycache__/views.cpython-312.pyc,,
browser_use/agent/message_manager/service.py,sha256=zA9zzMPpC0mgsUqaXOieoQDYMpGQwUUy9FRM17IU8fQ,12374
browser_use/agent/message_manager/utils.py,sha256=qSiwag8QiE9swT_5yUgyyCHOOUVYCEp5JP7ma13fgb4,4979
browser_use/agent/message_manager/views.py,sha256=wCyUJX6GfQMxErGkx8lUuZ-yqrdzUTIXkL-qiAspqfo,4093
browser_use/agent/playwright_script_generator.py,sha256=d7lEmna8h9keMqDqTaCZ73Vm2xMuvZ2RtceE7Av9kto,32432
browser_use/agent/playwright_script_helpers.py,sha256=yxoMIX7V1HjIEnvPG5GDxXCSRnJsa-TqHtf9R2uXq7k,4123
browser_use/agent/prompts.py,sha256=DuveEcHpDgIQnxCO1aOt3QPgTTTCYw86djUosBBUHYk,6091
browser_use/agent/service.py,sha256=hd0tWChG2v_9aRBTHNpXX7ajxqrW-F1nlXU7K5vjKsc,55611
browser_use/agent/system_prompt.md,sha256=_ag08P22uv3iOHmPquS7Uanx6loGsRjM88JAWnUvnwU,5081
browser_use/agent/views.py,sha256=cxe6Ng4txNQepFhyCFc2juXYbKbudYU2B5PohCIe_m0,14433
browser_use/browser/__pycache__/browser.cpython-312.pyc,,
browser_use/browser/__pycache__/chrome.cpython-312.pyc,,
browser_use/browser/__pycache__/context.cpython-312.pyc,,
browser_use/browser/__pycache__/dolphin_service.cpython-312.pyc,,
browser_use/browser/__pycache__/views.cpython-312.pyc,,
browser_use/browser/browser.py,sha256=cEyxBP-XDesNjAeEEITBlMlgvg3WJsofR-Fj7Stcp-s,15732
browser_use/browser/chrome.py,sha256=cTS3kvvu4tl1IaB_9AjrRfpJaIQDcRjAhhCbszEvAUE,10527
browser_use/browser/context.py,sha256=ZhTCGOV8SAZJ5KQ8YypGWoi_Q9ODn5O1lRN90Ozcvk4,70353
browser_use/browser/dolphin_service.py,sha256=glLwp87fP4ZBeUfm-hrxyAO6t_tqro8qO5Ayo0n44z8,10294
browser_use/browser/utils/__pycache__/screen_resolution.cpython-312.pyc,,
browser_use/browser/utils/screen_resolution.py,sha256=2zByA5C2SlG_6tbAZfbdtlZTypTeP6ekOwaexoBcMRI,1284
browser_use/browser/views.py,sha256=mmVT0MdmciFUAiY4xJSuKsmpWUg3D8rBjVTtLn_r9GM,1313
browser_use/cli.py,sha256=T9Jcya8IO4Qwqzx9p6bWGuhmigHba9j7s_YbkRcOI9A,48149
browser_use/controller/__pycache__/service.cpython-312.pyc,,
browser_use/controller/__pycache__/views.cpython-312.pyc,,
browser_use/controller/registry/__pycache__/service.cpython-312.pyc,,
browser_use/controller/registry/__pycache__/views.cpython-312.pyc,,
browser_use/controller/registry/service.py,sha256=dYyXxyV7PqqB36P4PY1zfZlNSH3KZ9_C_yC2G7EIayQ,8765
browser_use/controller/registry/views.py,sha256=yf1aETLETOgsJCk0D2quhvvEUfR0_N_AvD_OcrFAFGs,4463
browser_use/controller/service.py,sha256=JvbNfBLDq837jUnefSIgLv5St3lwxjVW9fkRpcUcjEo,32840
browser_use/controller/views.py,sha256=f4ikW36BpasEnN9gHaQkdbhvxlmAID1_P0hN4xgD3S8,2537
browser_use/dom/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
browser_use/dom/__pycache__/__init__.cpython-312.pyc,,
browser_use/dom/__pycache__/service.cpython-312.pyc,,
browser_use/dom/__pycache__/views.cpython-312.pyc,,
browser_use/dom/buildDomTree.js,sha256=nMtuhF0aPXmAhP0EKhzRoR0XackAivVdpa0kryggbYU,48919
browser_use/dom/clickable_element_processor/__pycache__/service.cpython-312.pyc,,
browser_use/dom/clickable_element_processor/service.py,sha256=oFKPQw84HJCirAN_ic1lZrKBfwDvripRoAK7zWQ5eoE,2681
browser_use/dom/history_tree_processor/__pycache__/service.cpython-312.pyc,,
browser_use/dom/history_tree_processor/__pycache__/view.cpython-312.pyc,,
browser_use/dom/history_tree_processor/service.py,sha256=LElri3-bsiVaXgiZpj3TzOGDgw_WfHWOaTw9_QWDOVA,4143
browser_use/dom/history_tree_processor/view.py,sha256=W_1O4njhqaAtKkTUuH4mrjTy-Zsanq5ecbf2qGDHL10,1646
browser_use/dom/service.py,sha256=NoPWcVSt4t6v4GetcHTDNFielG_idRlZVokgvuhqAqM,5782
browser_use/dom/views.py,sha256=Gjr-sM4N_LCvDE5gf2pFtkpmVhJZw0gI9uJr7HlX2SY,7954
browser_use/exceptions.py,sha256=Bn79JaO4kwiJ7YKDbtiebrgaNvzHn6PgKmbaNf_XrpI,186
browser_use/logging_config.py,sha256=7sqxixUSU3noNs3f8m_J2y0hH5b0jIqWQdXnU2oH-Jk,4091
browser_use/telemetry/__pycache__/service.cpython-312.pyc,,
browser_use/telemetry/__pycache__/views.cpython-312.pyc,,
browser_use/telemetry/service.py,sha256=-EvSqoX2bx3-PTV3LPYh5nXrcgFDrvUAEFxfqMLEDvc,3348
browser_use/telemetry/views.py,sha256=yQmEY0-CkyAQLoUChsx4U-RvK_OrDzYJF-FRf9V1NHA,1196
browser_use/utils.py,sha256=ZMxarI-QBznYNNv050wPwWhZOyukEh2WzFOezwZRB-s,11853
