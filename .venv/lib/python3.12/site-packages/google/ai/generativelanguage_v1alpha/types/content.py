# -*- coding: utf-8 -*-
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
from __future__ import annotations

from typing import MutableMapping, MutableSequence

from google.protobuf import struct_pb2  # type: ignore
import proto  # type: ignore

__protobuf__ = proto.module(
    package="google.ai.generativelanguage.v1alpha",
    manifest={
        "Type",
        "Content",
        "Part",
        "Blob",
        "FileData",
        "ExecutableCode",
        "CodeExecutionResult",
        "Tool",
        "GoogleSearchRetrieval",
        "DynamicRetrievalConfig",
        "CodeExecution",
        "ToolConfig",
        "FunctionCallingConfig",
        "FunctionDeclaration",
        "FunctionCall",
        "FunctionResponse",
        "Schema",
        "GroundingPassage",
        "GroundingPassages",
    },
)


class Type(proto.Enum):
    r"""Type contains the list of OpenAPI data types as defined by
    https://spec.openapis.org/oas/v3.0.3#data-types

    Values:
        TYPE_UNSPECIFIED (0):
            Not specified, should not be used.
        STRING (1):
            String type.
        NUMBER (2):
            Number type.
        INTEGER (3):
            Integer type.
        BOOLEAN (4):
            Boolean type.
        ARRAY (5):
            Array type.
        OBJECT (6):
            Object type.
    """
    TYPE_UNSPECIFIED = 0
    STRING = 1
    NUMBER = 2
    INTEGER = 3
    BOOLEAN = 4
    ARRAY = 5
    OBJECT = 6


class Content(proto.Message):
    r"""The base structured datatype containing multi-part content of a
    message.

    A ``Content`` includes a ``role`` field designating the producer of
    the ``Content`` and a ``parts`` field containing multi-part data
    that contains the content of the message turn.

    Attributes:
        parts (MutableSequence[google.ai.generativelanguage_v1alpha.types.Part]):
            Ordered ``Parts`` that constitute a single message. Parts
            may have different MIME types.
        role (str):
            Optional. The producer of the content. Must
            be either 'user' or 'model'.
            Useful to set for multi-turn conversations,
            otherwise can be left blank or unset.
    """

    parts: MutableSequence["Part"] = proto.RepeatedField(
        proto.MESSAGE,
        number=1,
        message="Part",
    )
    role: str = proto.Field(
        proto.STRING,
        number=2,
    )


class Part(proto.Message):
    r"""A datatype containing media that is part of a multi-part ``Content``
    message.

    A ``Part`` consists of data which has an associated datatype. A
    ``Part`` can only contain one of the accepted types in
    ``Part.data``.

    A ``Part`` must have a fixed IANA MIME type identifying the type and
    subtype of the media if the ``inline_data`` field is filled with raw
    bytes.

    This message has `oneof`_ fields (mutually exclusive fields).
    For each oneof, at most one member field can be set at the same time.
    Setting any member of the oneof automatically clears all other
    members.

    .. _oneof: https://proto-plus-python.readthedocs.io/en/stable/fields.html#oneofs-mutually-exclusive-fields

    Attributes:
        text (str):
            Inline text.

            This field is a member of `oneof`_ ``data``.
        inline_data (google.ai.generativelanguage_v1alpha.types.Blob):
            Inline media bytes.

            This field is a member of `oneof`_ ``data``.
        function_call (google.ai.generativelanguage_v1alpha.types.FunctionCall):
            A predicted ``FunctionCall`` returned from the model that
            contains a string representing the
            ``FunctionDeclaration.name`` with the arguments and their
            values.

            This field is a member of `oneof`_ ``data``.
        function_response (google.ai.generativelanguage_v1alpha.types.FunctionResponse):
            The result output of a ``FunctionCall`` that contains a
            string representing the ``FunctionDeclaration.name`` and a
            structured JSON object containing any output from the
            function is used as context to the model.

            This field is a member of `oneof`_ ``data``.
        file_data (google.ai.generativelanguage_v1alpha.types.FileData):
            URI based data.

            This field is a member of `oneof`_ ``data``.
        executable_code (google.ai.generativelanguage_v1alpha.types.ExecutableCode):
            Code generated by the model that is meant to
            be executed.

            This field is a member of `oneof`_ ``data``.
        code_execution_result (google.ai.generativelanguage_v1alpha.types.CodeExecutionResult):
            Result of executing the ``ExecutableCode``.

            This field is a member of `oneof`_ ``data``.
    """

    text: str = proto.Field(
        proto.STRING,
        number=2,
        oneof="data",
    )
    inline_data: "Blob" = proto.Field(
        proto.MESSAGE,
        number=3,
        oneof="data",
        message="Blob",
    )
    function_call: "FunctionCall" = proto.Field(
        proto.MESSAGE,
        number=4,
        oneof="data",
        message="FunctionCall",
    )
    function_response: "FunctionResponse" = proto.Field(
        proto.MESSAGE,
        number=5,
        oneof="data",
        message="FunctionResponse",
    )
    file_data: "FileData" = proto.Field(
        proto.MESSAGE,
        number=6,
        oneof="data",
        message="FileData",
    )
    executable_code: "ExecutableCode" = proto.Field(
        proto.MESSAGE,
        number=9,
        oneof="data",
        message="ExecutableCode",
    )
    code_execution_result: "CodeExecutionResult" = proto.Field(
        proto.MESSAGE,
        number=10,
        oneof="data",
        message="CodeExecutionResult",
    )


class Blob(proto.Message):
    r"""Raw media bytes.

    Text should not be sent as raw bytes, use the 'text' field.

    Attributes:
        mime_type (str):
            The IANA standard MIME type of the source data. Examples:

            -  image/png
            -  image/jpeg If an unsupported MIME type is provided, an
               error will be returned. For a complete list of supported
               types, see `Supported file
               formats <https://ai.google.dev/gemini-api/docs/prompting_with_media#supported_file_formats>`__.
        data (bytes):
            Raw bytes for media formats.
    """

    mime_type: str = proto.Field(
        proto.STRING,
        number=1,
    )
    data: bytes = proto.Field(
        proto.BYTES,
        number=2,
    )


class FileData(proto.Message):
    r"""URI based data.

    Attributes:
        mime_type (str):
            Optional. The IANA standard MIME type of the
            source data.
        file_uri (str):
            Required. URI.
    """

    mime_type: str = proto.Field(
        proto.STRING,
        number=1,
    )
    file_uri: str = proto.Field(
        proto.STRING,
        number=2,
    )


class ExecutableCode(proto.Message):
    r"""Code generated by the model that is meant to be executed, and the
    result returned to the model.

    Only generated when using the ``CodeExecution`` tool, in which the
    code will be automatically executed, and a corresponding
    ``CodeExecutionResult`` will also be generated.

    Attributes:
        language (google.ai.generativelanguage_v1alpha.types.ExecutableCode.Language):
            Required. Programming language of the ``code``.
        code (str):
            Required. The code to be executed.
    """

    class Language(proto.Enum):
        r"""Supported programming languages for the generated code.

        Values:
            LANGUAGE_UNSPECIFIED (0):
                Unspecified language. This value should not
                be used.
            PYTHON (1):
                Python >= 3.10, with numpy and simpy
                available.
        """
        LANGUAGE_UNSPECIFIED = 0
        PYTHON = 1

    language: Language = proto.Field(
        proto.ENUM,
        number=1,
        enum=Language,
    )
    code: str = proto.Field(
        proto.STRING,
        number=2,
    )


class CodeExecutionResult(proto.Message):
    r"""Result of executing the ``ExecutableCode``.

    Only generated when using the ``CodeExecution``, and always follows
    a ``part`` containing the ``ExecutableCode``.

    Attributes:
        outcome (google.ai.generativelanguage_v1alpha.types.CodeExecutionResult.Outcome):
            Required. Outcome of the code execution.
        output (str):
            Optional. Contains stdout when code execution
            is successful, stderr or other description
            otherwise.
    """

    class Outcome(proto.Enum):
        r"""Enumeration of possible outcomes of the code execution.

        Values:
            OUTCOME_UNSPECIFIED (0):
                Unspecified status. This value should not be
                used.
            OUTCOME_OK (1):
                Code execution completed successfully.
            OUTCOME_FAILED (2):
                Code execution finished but with a failure. ``stderr``
                should contain the reason.
            OUTCOME_DEADLINE_EXCEEDED (3):
                Code execution ran for too long, and was
                cancelled. There may or may not be a partial
                output present.
        """
        OUTCOME_UNSPECIFIED = 0
        OUTCOME_OK = 1
        OUTCOME_FAILED = 2
        OUTCOME_DEADLINE_EXCEEDED = 3

    outcome: Outcome = proto.Field(
        proto.ENUM,
        number=1,
        enum=Outcome,
    )
    output: str = proto.Field(
        proto.STRING,
        number=2,
    )


class Tool(proto.Message):
    r"""Tool details that the model may use to generate response.

    A ``Tool`` is a piece of code that enables the system to interact
    with external systems to perform an action, or set of actions,
    outside of knowledge and scope of the model.

    Attributes:
        function_declarations (MutableSequence[google.ai.generativelanguage_v1alpha.types.FunctionDeclaration]):
            Optional. A list of ``FunctionDeclarations`` available to
            the model that can be used for function calling.

            The model or system does not execute the function. Instead
            the defined function may be returned as a
            [FunctionCall][google.ai.generativelanguage.v1alpha.Part.function_call]
            with arguments to the client side for execution. The model
            may decide to call a subset of these functions by populating
            [FunctionCall][google.ai.generativelanguage.v1alpha.Part.function_call]
            in the response. The next conversation turn may contain a
            [FunctionResponse][google.ai.generativelanguage.v1alpha.Part.function_response]
            with the
            [Content.role][google.ai.generativelanguage.v1alpha.Content.role]
            "function" generation context for the next model turn.
        google_search_retrieval (google.ai.generativelanguage_v1alpha.types.GoogleSearchRetrieval):
            Optional. Retrieval tool that is powered by
            Google search.
        code_execution (google.ai.generativelanguage_v1alpha.types.CodeExecution):
            Optional. Enables the model to execute code
            as part of generation.
        google_search (google.ai.generativelanguage_v1alpha.types.Tool.GoogleSearch):
            Optional. GoogleSearch tool type.
            Tool to support Google Search in Model. Powered
            by Google.
    """

    class GoogleSearch(proto.Message):
        r"""GoogleSearch tool type.
        Tool to support Google Search in Model. Powered by Google.

        """

    function_declarations: MutableSequence["FunctionDeclaration"] = proto.RepeatedField(
        proto.MESSAGE,
        number=1,
        message="FunctionDeclaration",
    )
    google_search_retrieval: "GoogleSearchRetrieval" = proto.Field(
        proto.MESSAGE,
        number=2,
        message="GoogleSearchRetrieval",
    )
    code_execution: "CodeExecution" = proto.Field(
        proto.MESSAGE,
        number=3,
        message="CodeExecution",
    )
    google_search: GoogleSearch = proto.Field(
        proto.MESSAGE,
        number=4,
        message=GoogleSearch,
    )


class GoogleSearchRetrieval(proto.Message):
    r"""Tool to retrieve public web data for grounding, powered by
    Google.

    Attributes:
        dynamic_retrieval_config (google.ai.generativelanguage_v1alpha.types.DynamicRetrievalConfig):
            Specifies the dynamic retrieval configuration
            for the given source.
    """

    dynamic_retrieval_config: "DynamicRetrievalConfig" = proto.Field(
        proto.MESSAGE,
        number=1,
        message="DynamicRetrievalConfig",
    )


class DynamicRetrievalConfig(proto.Message):
    r"""Describes the options to customize dynamic retrieval.

    .. _oneof: https://proto-plus-python.readthedocs.io/en/stable/fields.html#oneofs-mutually-exclusive-fields

    Attributes:
        mode (google.ai.generativelanguage_v1alpha.types.DynamicRetrievalConfig.Mode):
            The mode of the predictor to be used in
            dynamic retrieval.
        dynamic_threshold (float):
            The threshold to be used in dynamic
            retrieval. If not set, a system default value is
            used.

            This field is a member of `oneof`_ ``_dynamic_threshold``.
    """

    class Mode(proto.Enum):
        r"""The mode of the predictor to be used in dynamic retrieval.

        Values:
            MODE_UNSPECIFIED (0):
                Always trigger retrieval.
            MODE_DYNAMIC (1):
                Run retrieval only when system decides it is
                necessary.
        """
        MODE_UNSPECIFIED = 0
        MODE_DYNAMIC = 1

    mode: Mode = proto.Field(
        proto.ENUM,
        number=1,
        enum=Mode,
    )
    dynamic_threshold: float = proto.Field(
        proto.FLOAT,
        number=2,
        optional=True,
    )


class CodeExecution(proto.Message):
    r"""Tool that executes code generated by the model, and automatically
    returns the result to the model.

    See also ``ExecutableCode`` and ``CodeExecutionResult`` which are
    only generated when using this tool.

    """


class ToolConfig(proto.Message):
    r"""The Tool configuration containing parameters for specifying ``Tool``
    use in the request.

    Attributes:
        function_calling_config (google.ai.generativelanguage_v1alpha.types.FunctionCallingConfig):
            Optional. Function calling config.
    """

    function_calling_config: "FunctionCallingConfig" = proto.Field(
        proto.MESSAGE,
        number=1,
        message="FunctionCallingConfig",
    )


class FunctionCallingConfig(proto.Message):
    r"""Configuration for specifying function calling behavior.

    Attributes:
        mode (google.ai.generativelanguage_v1alpha.types.FunctionCallingConfig.Mode):
            Optional. Specifies the mode in which
            function calling should execute. If unspecified,
            the default value will be set to AUTO.
        allowed_function_names (MutableSequence[str]):
            Optional. A set of function names that, when provided,
            limits the functions the model will call.

            This should only be set when the Mode is ANY. Function names
            should match [FunctionDeclaration.name]. With mode set to
            ANY, model will predict a function call from the set of
            function names provided.
    """

    class Mode(proto.Enum):
        r"""Defines the execution behavior for function calling by
        defining the execution mode.

        Values:
            MODE_UNSPECIFIED (0):
                Unspecified function calling mode. This value
                should not be used.
            AUTO (1):
                Default model behavior, model decides to
                predict either a function call or a natural
                language response.
            ANY (2):
                Model is constrained to always predicting a function call
                only. If "allowed_function_names" are set, the predicted
                function call will be limited to any one of
                "allowed_function_names", else the predicted function call
                will be any one of the provided "function_declarations".
            NONE (3):
                Model will not predict any function call.
                Model behavior is same as when not passing any
                function declarations.
        """
        MODE_UNSPECIFIED = 0
        AUTO = 1
        ANY = 2
        NONE = 3

    mode: Mode = proto.Field(
        proto.ENUM,
        number=1,
        enum=Mode,
    )
    allowed_function_names: MutableSequence[str] = proto.RepeatedField(
        proto.STRING,
        number=2,
    )


class FunctionDeclaration(proto.Message):
    r"""Structured representation of a function declaration as defined by
    the `OpenAPI 3.03
    specification <https://spec.openapis.org/oas/v3.0.3>`__. Included in
    this declaration are the function name and parameters. This
    FunctionDeclaration is a representation of a block of code that can
    be used as a ``Tool`` by the model and executed by the client.


    .. _oneof: https://proto-plus-python.readthedocs.io/en/stable/fields.html#oneofs-mutually-exclusive-fields

    Attributes:
        name (str):
            Required. The name of the function.
            Must be a-z, A-Z, 0-9, or contain underscores
            and dashes, with a maximum length of 63.
        description (str):
            Required. A brief description of the
            function.
        parameters (google.ai.generativelanguage_v1alpha.types.Schema):
            Optional. Describes the parameters to this
            function. Reflects the Open API 3.03 Parameter
            Object string Key: the name of the parameter.
            Parameter names are case sensitive. Schema
            Value: the Schema defining the type used for the
            parameter.

            This field is a member of `oneof`_ ``_parameters``.
        response (google.ai.generativelanguage_v1alpha.types.Schema):
            Optional. Describes the output from this
            function in JSON Schema format. Reflects the
            Open API 3.03 Response Object. The Schema
            defines the type used for the response value of
            the function.

            This field is a member of `oneof`_ ``_response``.
    """

    name: str = proto.Field(
        proto.STRING,
        number=1,
    )
    description: str = proto.Field(
        proto.STRING,
        number=2,
    )
    parameters: "Schema" = proto.Field(
        proto.MESSAGE,
        number=3,
        optional=True,
        message="Schema",
    )
    response: "Schema" = proto.Field(
        proto.MESSAGE,
        number=4,
        optional=True,
        message="Schema",
    )


class FunctionCall(proto.Message):
    r"""A predicted ``FunctionCall`` returned from the model that contains a
    string representing the ``FunctionDeclaration.name`` with the
    arguments and their values.


    .. _oneof: https://proto-plus-python.readthedocs.io/en/stable/fields.html#oneofs-mutually-exclusive-fields

    Attributes:
        id (str):
            Optional. The unique id of the function call. If populated,
            the client to execute the ``function_call`` and return the
            response with the matching ``id``.
        name (str):
            Required. The name of the function to call.
            Must be a-z, A-Z, 0-9, or contain underscores
            and dashes, with a maximum length of 63.
        args (google.protobuf.struct_pb2.Struct):
            Optional. The function parameters and values
            in JSON object format.

            This field is a member of `oneof`_ ``_args``.
    """

    id: str = proto.Field(
        proto.STRING,
        number=3,
    )
    name: str = proto.Field(
        proto.STRING,
        number=1,
    )
    args: struct_pb2.Struct = proto.Field(
        proto.MESSAGE,
        number=2,
        optional=True,
        message=struct_pb2.Struct,
    )


class FunctionResponse(proto.Message):
    r"""The result output from a ``FunctionCall`` that contains a string
    representing the ``FunctionDeclaration.name`` and a structured JSON
    object containing any output from the function is used as context to
    the model. This should contain the result of a\ ``FunctionCall``
    made based on model prediction.

    Attributes:
        id (str):
            Optional. The id of the function call this response is for.
            Populated by the client to match the corresponding function
            call ``id``.
        name (str):
            Required. The name of the function to call.
            Must be a-z, A-Z, 0-9, or contain underscores
            and dashes, with a maximum length of 63.
        response (google.protobuf.struct_pb2.Struct):
            Required. The function response in JSON
            object format.
    """

    id: str = proto.Field(
        proto.STRING,
        number=3,
    )
    name: str = proto.Field(
        proto.STRING,
        number=1,
    )
    response: struct_pb2.Struct = proto.Field(
        proto.MESSAGE,
        number=2,
        message=struct_pb2.Struct,
    )


class Schema(proto.Message):
    r"""The ``Schema`` object allows the definition of input and output data
    types. These types can be objects, but also primitives and arrays.
    Represents a select subset of an `OpenAPI 3.0 schema
    object <https://spec.openapis.org/oas/v3.0.3#schema>`__.


    .. _oneof: https://proto-plus-python.readthedocs.io/en/stable/fields.html#oneofs-mutually-exclusive-fields

    Attributes:
        type_ (google.ai.generativelanguage_v1alpha.types.Type):
            Required. Data type.
        format_ (str):
            Optional. The format of the data. This is
            used only for primitive datatypes. Supported
            formats:

             for NUMBER type: float, double
             for INTEGER type: int32, int64
             for STRING type: enum
        description (str):
            Optional. A brief description of the
            parameter. This could contain examples of use.
            Parameter description may be formatted as
            Markdown.
        nullable (bool):
            Optional. Indicates if the value may be null.
        enum (MutableSequence[str]):
            Optional. Possible values of the element of Type.STRING with
            enum format. For example we can define an Enum Direction as
            : {type:STRING, format:enum, enum:["EAST", NORTH", "SOUTH",
            "WEST"]}
        items (google.ai.generativelanguage_v1alpha.types.Schema):
            Optional. Schema of the elements of
            Type.ARRAY.

            This field is a member of `oneof`_ ``_items``.
        max_items (int):
            Optional. Maximum number of the elements for
            Type.ARRAY.
        min_items (int):
            Optional. Minimum number of the elements for
            Type.ARRAY.
        properties (MutableMapping[str, google.ai.generativelanguage_v1alpha.types.Schema]):
            Optional. Properties of Type.OBJECT.
        required (MutableSequence[str]):
            Optional. Required properties of Type.OBJECT.
    """

    type_: "Type" = proto.Field(
        proto.ENUM,
        number=1,
        enum="Type",
    )
    format_: str = proto.Field(
        proto.STRING,
        number=2,
    )
    description: str = proto.Field(
        proto.STRING,
        number=3,
    )
    nullable: bool = proto.Field(
        proto.BOOL,
        number=4,
    )
    enum: MutableSequence[str] = proto.RepeatedField(
        proto.STRING,
        number=5,
    )
    items: "Schema" = proto.Field(
        proto.MESSAGE,
        number=6,
        optional=True,
        message="Schema",
    )
    max_items: int = proto.Field(
        proto.INT64,
        number=21,
    )
    min_items: int = proto.Field(
        proto.INT64,
        number=22,
    )
    properties: MutableMapping[str, "Schema"] = proto.MapField(
        proto.STRING,
        proto.MESSAGE,
        number=7,
        message="Schema",
    )
    required: MutableSequence[str] = proto.RepeatedField(
        proto.STRING,
        number=8,
    )


class GroundingPassage(proto.Message):
    r"""Passage included inline with a grounding configuration.

    Attributes:
        id (str):
            Identifier for the passage for attributing
            this passage in grounded answers.
        content (google.ai.generativelanguage_v1alpha.types.Content):
            Content of the passage.
    """

    id: str = proto.Field(
        proto.STRING,
        number=1,
    )
    content: "Content" = proto.Field(
        proto.MESSAGE,
        number=2,
        message="Content",
    )


class GroundingPassages(proto.Message):
    r"""A repeated list of passages.

    Attributes:
        passages (MutableSequence[google.ai.generativelanguage_v1alpha.types.GroundingPassage]):
            List of passages.
    """

    passages: MutableSequence["GroundingPassage"] = proto.RepeatedField(
        proto.MESSAGE,
        number=1,
        message="GroundingPassage",
    )


__all__ = tuple(sorted(__protobuf__.manifest))
