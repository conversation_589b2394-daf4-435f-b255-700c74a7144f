#!/usr/bin/env python3
"""
Test script to verify the web-UI is working properly
"""
import requests
import time

def test_webui_connection():
    print("🧪 Testing Web-UI Connection...")
    
    url = "http://127.0.0.1:7788"
    
    try:
        print(f"📡 Connecting to {url}...")
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print("✅ Web-UI is responding!")
            print(f"📊 Status Code: {response.status_code}")
            print(f"📏 Response Length: {len(response.text)} characters")
            
            # Check if it's a Gradio app
            if "gradio" in response.text.lower():
                print("✅ Gradio interface detected!")
            
            # Check for browser-use specific content
            if "browser" in response.text.lower():
                print("✅ Browser-use content detected!")
                
            return True
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection refused - Web-UI is not running")
        return False
    except requests.exceptions.Timeout:
        print("❌ Connection timeout")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_webui_connection()
    
    if success:
        print("\n🎉 Web-UI is working correctly!")
        print("💡 You can now:")
        print("   1. Open http://127.0.0.1:7788 in your browser")
        print("   2. Use the Electron app")
        print("   3. Submit browser automation tasks")
        print("\n⚠️  Just need a valid Grok API key for full functionality!")
    else:
        print("\n❌ Web-UI connection failed")
        print("💡 Try restarting the Python web-UI server")
