# test_browser_use.py
import asyncio
import os
from dotenv import load_dotenv
load_dotenv()

from browser_use import Agent

async def main():
    print("🧪 Testing browser-use setup...")

    # Check if we have any valid API keys
    api_keys_available = {
        "OpenAI": bool(os.getenv("OPENAI_API_KEY")),
        "Anthropic": bool(os.getenv("ANTHROPIC_API_KEY")),
        "Google": bool(os.getenv("GOOGLE_API_KEY")),
        "Grok": bool(os.getenv("GROK_API_KEY"))
    }

    print("API Keys status:")
    for provider, available in api_keys_available.items():
        status = "✅" if available else "❌"
        print(f"  {status} {provider}")

    if not any(api_keys_available.values()):
        print("\n❌ No API keys found!")
        print("To test browser-use, please set one of these environment variables:")
        print("  - OPENAI_API_KEY=your_openai_key")
        print("  - ANTHROPIC_API_KEY=your_anthropic_key")
        print("  - GOOGLE_API_KEY=your_google_key")
        print("  - GROK_API_KEY=your_grok_key")
        print("\nYou can add them to the .env file in this directory.")
        return

    # Try to get a working LLM
    llm = None

    # Try OpenAI first (most reliable)
    if os.getenv("OPENAI_API_KEY"):
        try:
            from langchain_openai import ChatOpenAI
            llm = ChatOpenAI(model="gpt-4o-mini", temperature=0.1)
            print("\n🤖 Using OpenAI GPT-4o-mini")
        except Exception as e:
            print(f"❌ OpenAI setup failed: {e}")

    # Try Anthropic
    elif os.getenv("ANTHROPIC_API_KEY"):
        try:
            from langchain_anthropic import ChatAnthropic
            llm = ChatAnthropic(model="claude-3-haiku-20240307", temperature=0.1)
            print("\n🤖 Using Anthropic Claude Haiku")
        except Exception as e:
            print(f"❌ Anthropic setup failed: {e}")

    # Try Google
    elif os.getenv("GOOGLE_API_KEY"):
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI
            llm = ChatGoogleGenerativeAI(model="gemini-1.5-flash", temperature=0.1)
            print("\n🤖 Using Google Gemini Flash")
        except Exception as e:
            print(f"❌ Google setup failed: {e}")

    if llm is None:
        print("❌ Could not initialize any LLM. Please check your API keys.")
        return

    try:
        print("🚀 Creating browser-use agent...")
        # Configure your agent
        agent = Agent(
            task="Navigate to google.com and search for 'hello world'",
            llm=llm,
            use_vision=False,  # Disable vision for compatibility
            enable_memory=False,  # Disable memory to avoid dependencies
        )

        print("✅ Agent created successfully!")
        print("🌐 Starting browser automation...")

        # This will start the browser automation
        result = await agent.run()
        print("\n🎉 Task completed!")
        print("📊 Results:")
        print(result)

    except Exception as e:
        print(f"❌ Agent execution failed: {e}")
        import traceback
        traceback.print_exc()

    # This will start the browser automation
    result = await agent.run()
    print(result)

if __name__ == "__main__":
    asyncio.run(main())
