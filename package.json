{"name": "browser-use-desktop", "productName": "browser-use-desktop", "version": "1.0.0", "description": "My Electron application description", "main": ".vite/build/main.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint --ext .ts,.tsx ."}, "keywords": [], "author": {"name": "<PERSON>", "email": "***************"}, "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.8.0", "@electron-forge/maker-deb": "^7.8.0", "@electron-forge/maker-rpm": "^7.8.0", "@electron-forge/maker-squirrel": "^7.8.0", "@electron-forge/maker-zip": "^7.8.0", "@electron-forge/plugin-auto-unpack-natives": "^7.8.0", "@electron-forge/plugin-fuses": "^7.8.0", "@electron-forge/plugin-vite": "^7.8.0", "@electron/fuses": "^1.8.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "electron": "35.1.2", "eslint": "^8.57.1", "eslint-plugin-import": "^2.31.0", "ts-node": "^10.9.2", "typescript": "~4.5.4", "vite": "^5.4.15"}, "dependencies": {"electron-squirrel-startup": "^1.0.1"}}